<!doctype html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <!-- <link rel="icon" type="image/svg+xml" href="/vite.svg" /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SmartBoutique - Gestion de Boutique</title>
    <meta name="description" content="Application de gestion de boutique moderne et complète" />
    
    <!-- <PERSON>o Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap"
    />
    
    <!-- Material Icons -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
    />
    
    <style>
      body {
        margin: 0;
        font-family: 'Roboto', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      #root {
        min-height: 100vh;
      }
      
      /* Loading styles */
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        font-family: 'Roboto', sans-serif;
        font-size: 18px;
        color: #666;
      }

      /* Print styles for receipts */
      @media print {
        /* Reset all elements for clean printing */
        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        /* Hide everything by default */
        body > *:not(.receipt-print-container) {
          display: none !important;
        }

        /* Hide all Material-UI components that shouldn't print */
        .MuiDialog-root,
        .MuiBackdrop-root,
        .MuiAppBar-root,
        .MuiDrawer-root,
        .MuiDialogActions-root,
        .MuiDialogTitle-root,
        .MuiButton-root,
        .MuiIconButton-root,
        .MuiFab-root,
        .MuiSnackbar-root,
        .print-hide,
        nav,
        header,
        footer {
          display: none !important;
        }

        /* Show only receipt content */
        .receipt-print-container {
          display: block !important;
          position: static !important;
          width: 100% !important;
          height: auto !important;
          margin: 0 !important;
          padding: 0 !important;
          background: white !important;
          overflow: visible !important;
        }

        .receipt-container {
          display: block !important;
          position: static !important;
          width: 100% !important;
          height: auto !important;
          margin: 0 !important;
          padding: 5mm !important;
          background: white !important;
          color: black !important;
          font-family: 'Courier New', monospace !important;
          font-size: 12px !important;
          line-height: 1.3 !important;
          page-break-inside: avoid;
          box-shadow: none !important;
          border: none !important;
        }

        /* Thermal receipt styles (80mm) */
        .thermal-receipt {
          width: 80mm !important;
          max-width: 80mm !important;
          font-size: 11px !important;
          line-height: 1.2 !important;
        }

        /* A4 receipt styles */
        .a4-receipt {
          width: 190mm !important;
          max-width: 190mm !important;
          font-family: 'Arial', sans-serif !important;
          font-size: 13px !important;
          line-height: 1.4 !important;
          padding: 10mm !important;
        }

        /* Ensure all text is black */
        .receipt-container,
        .receipt-container * {
          color: black !important;
          background: transparent !important;
        }

        /* Remove Material-UI Paper styling */
        .MuiPaper-root {
          box-shadow: none !important;
          border: none !important;
          background: white !important;
        }

        /* Typography overrides */
        .MuiTypography-root {
          color: black !important;
        }

        /* Table styling for print */
        .MuiTable-root,
        .MuiTableContainer-root {
          background: white !important;
        }

        .MuiTableCell-root {
          border-color: black !important;
          color: black !important;
          padding: 2px 4px !important;
        }

        /* Divider styling */
        .MuiDivider-root {
          border-color: black !important;
          background-color: black !important;
        }
      }

      /* Page settings for thermal printing */
      @page {
        margin: 0;
        size: 80mm auto;
      }

      /* Page settings for A4 printing */
      @media print and (min-width: 200mm) {
        @page {
          size: A4;
          margin: 10mm;
        }
      }
    </style>
    <script type="module" crossorigin src="./assets/index-ChozIUZc.js"></script>
  </head>
  <body>
    <div id="root">
      <div class="loading">Chargement de SmartBoutique...</div>
    </div>
  </body>
</html>
